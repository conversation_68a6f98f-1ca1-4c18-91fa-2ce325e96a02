"""
Repository层覆盖率提升测试
========================

专门针对提高Repository层代码覆盖率的测试
测试之前未覆盖的代码路径和边界情况
"""

from datetime import datetime, timedelta
import uuid

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.applicant_repository import ApplicantRepository
from app.repositories.application_repository import ApplicationRepository
from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestOrderRepositoryCoverageBoost:
    """专门提升OrderRepository覆盖率的测试"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_get_orders_by_status_with_user_filter(
        self, order_repo: OrderRepository, test_user
    ):
        """测试按状态和用户过滤的订单查询"""
        # 创建不同状态的订单
        statuses = [OrderStatus.CREATED, OrderStatus.CANCELLED]
        created_orders = []

        for status in statuses:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": status.value,
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试按状态查询
        created_orders_result = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders_result) >= 1
        assert all(
            o.order_status == OrderStatus.CREATED.value for o in created_orders_result
        )

        # 测试不指定用户的查询
        all_created_orders = await order_repo.get_orders_by_status(OrderStatus.CREATED)
        assert len(all_created_orders) >= len(created_orders_result)

    @pytest.mark.asyncio
    async def test_get_order_stats_detailed(
        self, order_repo: OrderRepository, test_user
    ):
        """测试详细的订单统计功能"""
        # 创建多种状态的订单
        test_data = [
            (OrderStatus.CREATED, 3),
            (OrderStatus.CANCELLED, 2),
        ]

        for status, count in test_data:
            for i in range(count):
                order = TestDataFactory.create_order_data(
                    {
                        "user_id": test_user.id,
                        "order_status": status.value,
                        "order_no": f"VN20250711{status.value.upper()}{str(i).zfill(3)}",
                    }
                )
                await order_repo.create(order)

        # 测试用户统计
        user_stats = await order_repo.get_order_stats(test_user.id)
        assert user_stats["total_orders"] == 5
        assert user_stats["created_orders"] == 3
        assert user_stats["cancelled_orders"] == 2

        # 测试全局统计
        global_stats = await order_repo.get_order_stats()
        assert global_stats["total_orders"] >= 5

    @pytest.mark.asyncio
    async def test_pagination_edge_cases(self, order_repo: OrderRepository, test_user):
        """测试分页查询的边界情况"""
        # 创建足够多的订单来测试分页
        total_orders = 7
        for i in range(total_orders):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711PAGE{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试第一页
        page1 = await order_repo.get_by_user_id(test_user.id, limit=3, offset=0)
        assert len(page1) == 3

        # 测试中间页
        page2 = await order_repo.get_by_user_id(test_user.id, limit=3, offset=3)
        assert len(page2) == 3

        # 测试最后一页（不完整页）
        page3 = await order_repo.get_by_user_id(test_user.id, limit=3, offset=6)
        assert len(page3) == 1

        # 测试超出范围的页
        page4 = await order_repo.get_by_user_id(test_user.id, limit=3, offset=10)
        assert len(page4) == 0

        # 测试无限制查询
        all_orders = await order_repo.get_by_user_id(test_user.id)
        assert len(all_orders) >= total_orders

    @pytest.mark.asyncio
    async def test_order_update_operations(
        self, order_repo: OrderRepository, test_user
    ):
        """测试订单更新操作"""
        # 创建订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CREATED.value,
            }
        )
        created_order = await order_repo.create(order)

        # 测试更新订单状态
        created_order.order_status = OrderStatus.CANCELLED.value
        created_order.updated_at = datetime.now()
        updated_order = await order_repo.update(created_order)

        assert updated_order.order_status == OrderStatus.CANCELLED.value

        # 验证更新后的数据
        fetched_order = await order_repo.get_by_id(created_order.id)
        assert fetched_order.order_status == OrderStatus.CANCELLED.value

    @pytest.mark.asyncio
    async def test_order_deletion(self, order_repo: OrderRepository, test_user):
        """测试订单删除操作"""
        # 创建订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
            }
        )
        created_order = await order_repo.create(order)
        order_id = created_order.id

        # 测试删除
        deleted = await order_repo.delete(order_id)
        assert deleted is True

        # 验证删除结果
        deleted_order = await order_repo.get_by_id(order_id)
        assert deleted_order is None

    @pytest.mark.asyncio
    async def test_order_search_functionality(
        self, order_repo: OrderRepository, test_user
    ):
        """测试订单搜索功能"""
        # 创建具有特定申请人姓名的订单
        test_names = ["张三丰", "李小龙", "王大锤"]
        created_orders = []

        for name in test_names:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "applicant_name": name,
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试精确搜索
        zhang_orders = await order_repo.search_by_applicant_name("张三丰")
        assert len(zhang_orders) >= 1
        assert any("张三丰" in order.applicant_name for order in zhang_orders)

        # 测试模糊搜索
        li_orders = await order_repo.search_by_applicant_name("李")
        assert len(li_orders) >= 1

    @pytest.mark.asyncio
    async def test_order_date_filtering(self, order_repo: OrderRepository, test_user):
        """测试按日期过滤订单"""
        # 创建不同日期的订单
        dates = [
            datetime.now() - timedelta(days=1),
            datetime.now() - timedelta(days=3),
            datetime.now() - timedelta(days=7),
        ]

        created_orders = []
        for i, created_at in enumerate(dates):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711DATE{str(i).zfill(3)}",
                }
            )
            # 手动设置创建时间
            order.created_at = created_at
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试日期范围查询
        start_date = datetime.now() - timedelta(days=5)
        end_date = datetime.now()

        recent_orders = await order_repo.get_orders_by_date_range(
            start_date, end_date, user_id=test_user.id
        )
        # 应该包含1天前和3天前的订单，但不包含7天前的
        assert len(recent_orders) >= 2

    @pytest.mark.asyncio
    async def test_error_handling_scenarios(
        self, order_repo: OrderRepository, test_user
    ):
        """测试各种错误处理场景"""
        # 测试查询不存在的订单
        non_existent_order = await order_repo.get_by_id(uuid.uuid4())
        assert non_existent_order is None

        # 测试查询不存在的订单号
        non_existent_order_no = await order_repo.get_by_order_no(
            "VN20250101NOTEXIST", test_user.id
        )
        assert non_existent_order_no is None

        # 测试无效用户ID的查询
        fake_user_id = uuid.uuid4()
        user_orders = await order_repo.get_by_user_id(fake_user_id)
        assert len(user_orders) == 0

    @pytest.mark.asyncio
    async def test_constraint_violations(self, order_repo: OrderRepository, test_user):
        """测试数据库约束违反"""
        # 创建订单
        unique_order_no = "VN20250711UNIQUE001"
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": unique_order_no,
            }
        )
        await order_repo.create(order1)

        # 尝试创建重复订单号的订单
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": unique_order_no,  # 重复的订单号
            }
        )

        with pytest.raises(IntegrityError):
            await order_repo.create(order2)


class TestApplicantRepositoryCoverageBoost:
    """提升ApplicantRepository覆盖率的测试"""

    @pytest.fixture
    async def applicant_repo(self, db_session: AsyncSession):
        return ApplicantRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_applicant_crud_operations(
        self, applicant_repo: ApplicantRepository, test_user
    ):
        """测试申请人的CRUD操作"""
        # Create
        applicant = TestDataFactory.create_applicant_data(
            {
                "user_id": test_user.id,
                "name": "测试申请人",
                "passport_number": "E12345678",
            }
        )
        created_applicant = await applicant_repo.create(applicant)
        assert created_applicant.id is not None

        # Read
        found_applicant = await applicant_repo.get_by_id(created_applicant.id)
        assert found_applicant is not None
        assert found_applicant.name == "测试申请人"

        # Update
        found_applicant.name = "更新后的申请人"
        updated_applicant = await applicant_repo.update(found_applicant)
        assert updated_applicant.name == "更新后的申请人"

        # Delete
        deleted = await applicant_repo.delete(created_applicant.id)
        assert deleted is True

    @pytest.mark.asyncio
    async def test_applicant_search_operations(
        self, applicant_repo: ApplicantRepository, test_user
    ):
        """测试申请人搜索操作"""
        # 创建多个申请人
        applicants_data = [
            {"name": "张三", "passport_number": "E11111111"},
            {"name": "李四", "passport_number": "E22222222"},
            {"name": "王五", "passport_number": "E33333333"},
        ]

        for data in applicants_data:
            applicant = TestDataFactory.create_applicant_data(
                {"user_id": test_user.id, **data}
            )
            await applicant_repo.create(applicant)

        # 测试按用户查询
        user_applicants = await applicant_repo.get_by_user_id(test_user.id)
        assert len(user_applicants) >= 3

        # 测试按护照号查询
        zhang_applicant = await applicant_repo.get_by_passport_number(
            "E11111111", test_user.id
        )
        assert zhang_applicant is not None
        assert zhang_applicant.name == "张三"

        # 测试按姓名搜索
        li_applicants = await applicant_repo.search_by_name("李四")
        assert len(li_applicants) >= 1


class TestApplicationRepositoryCoverageBoost:
    """提升ApplicationRepository覆盖率的测试"""

    @pytest.fixture
    async def application_repo(self, db_session: AsyncSession):
        return ApplicationRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_application_basic_operations(
        self, application_repo: ApplicationRepository, test_user
    ):
        """测试申请记录的基本操作"""
        # 创建申请记录
        application = TestDataFactory.create_application_data(
            {
                "user_id": test_user.id,
                "application_number": "VN20250711APP001",
            }
        )
        created_application = await application_repo.create(application)
        assert created_application.id is not None

        # 查询申请记录
        found_application = await application_repo.get_by_id(created_application.id)
        assert found_application is not None

        # 按用户查询申请记录
        user_applications = await application_repo.get_by_user_id(test_user.id)
        assert len(user_applications) >= 1

    @pytest.mark.asyncio
    async def test_application_status_operations(
        self, application_repo: ApplicationRepository, test_user
    ):
        """测试申请状态相关操作"""
        # 创建申请记录
        application = TestDataFactory.create_application_data(
            {
                "user_id": test_user.id,
                "visa_status": "submitted",
            }
        )
        created_application = await application_repo.create(application)

        # 更新状态
        created_application.visa_status = "approved"
        updated_application = await application_repo.update(created_application)
        assert updated_application.visa_status == "approved"

        # 按状态查询
        approved_applications = await application_repo.get_by_status(
            "approved", user_id=test_user.id
        )
        assert len(approved_applications) >= 1
