"""
增强的Repository层测试 - 提高真实代码覆盖率
==========================================

专注于测试Repository层的复杂查询、事务处理、错误处理
使用真实数据库操作，避免Mock，提高代码覆盖率从23%到80%+
"""

from datetime import datetime, timedelta

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestOrderRepositoryEnhanced:
    """增强的订单Repository测试 - 提高覆盖率"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        """创建OrderRepository实例"""
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """创建测试用户"""
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_complex_order_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试复杂的订单查询逻辑"""
        # 创建多个不同状态和时间的订单 - 只使用实际存在的状态
        orders_data = [
            {"status": OrderStatus.CREATED, "days_ago": 1},
            {"status": OrderStatus.CANCELLED, "days_ago": 3},
        ]

        created_orders = []
        for data in orders_data:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": data["status"].value,
                    "created_at": datetime.now() - timedelta(days=data["days_ago"]),
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试按状态查询
        paid_orders = await order_repo.get_by_status(OrderStatus.PAID)
        assert len(paid_orders) >= 1
        assert all(
            order.order_status == OrderStatus.PAID.value for order in paid_orders
        )

        # 测试按用户和状态查询
        user_paid_orders = await order_repo.get_by_user_and_status(
            test_user.id, OrderStatus.PAID
        )
        assert len(user_paid_orders) == 1

        # 测试日期范围查询
        start_date = datetime.now() - timedelta(days=6)
        end_date = datetime.now()
        recent_orders = await order_repo.get_by_date_range(start_date, end_date)
        assert len(recent_orders) >= 3  # 应该包含1、3、5天前的订单

    @pytest.mark.asyncio
    async def test_order_statistics_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单统计查询功能"""
        # 创建测试数据
        statuses = [OrderStatus.CREATED, OrderStatus.PAID, OrderStatus.CANCELLED]
        for status in statuses:
            for i in range(2):  # 每种状态创建2个订单
                order = TestDataFactory.create_order_data(
                    {
                        "user_id": test_user.id,
                        "order_status": status.value,
                    }
                )
                await order_repo.create(order)

        # 测试用户订单统计
        stats = await order_repo.get_order_stats(test_user.id)
        assert stats["total_orders"] == 6
        assert stats["created_orders"] == 2
        assert stats["paid_orders"] == 2
        assert stats["cancelled_orders"] == 2

        # 测试全局统计
        global_stats = await order_repo.get_global_stats()
        assert global_stats["total_orders"] >= 6

    @pytest.mark.asyncio
    async def test_order_search_functionality(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单搜索功能"""
        # 创建具有特定申请人姓名的订单
        search_names = ["张三", "李四", "王五"]
        created_orders = []

        for name in search_names:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "applicant_name": name,
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试按申请人姓名搜索
        zhang_orders = await order_repo.search_by_applicant_name("张三")
        assert len(zhang_orders) >= 1
        assert all("张三" in order.applicant_name for order in zhang_orders)

        # 测试模糊搜索
        li_orders = await order_repo.search_by_applicant_name("李")
        assert len(li_orders) >= 1

    @pytest.mark.asyncio
    async def test_order_batch_operations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试批量操作功能"""
        # 创建多个订单
        order_ids = []
        for i in range(5):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": OrderStatus.CREATED.value,
                }
            )
            created_order = await order_repo.create(order)
            order_ids.append(created_order.id)

        # 测试批量状态更新
        updated_count = await order_repo.batch_update_status(
            order_ids, OrderStatus.PAID
        )
        assert updated_count == 5

        # 验证更新结果
        for order_id in order_ids:
            order = await order_repo.get_by_id(order_id)
            assert order.order_status == OrderStatus.PAID.value

    @pytest.mark.asyncio
    async def test_order_soft_delete(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试软删除功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)
        order_id = created_order.id

        # 执行软删除
        deleted = await order_repo.soft_delete(order_id)
        assert deleted is True

        # 验证软删除结果
        order = await order_repo.get_by_id(order_id)
        assert order is None  # 正常查询应该找不到

        # 测试包含已删除记录的查询
        order_with_deleted = await order_repo.get_by_id_include_deleted(order_id)
        assert order_with_deleted is not None
        assert order_with_deleted.deleted_at is not None

    @pytest.mark.asyncio
    async def test_order_pagination_edge_cases(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试分页查询的边界情况"""
        # 创建大量订单
        total_orders = 25
        for i in range(total_orders):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711TEST{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试第一页
        page1 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=0)
        assert len(page1) == 10

        # 测试中间页
        page2 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=10)
        assert len(page2) == 10

        # 测试最后一页
        page3 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=20)
        assert len(page3) == 5  # 剩余5个

        # 测试超出范围的页
        page4 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=30)
        assert len(page4) == 0

    @pytest.mark.asyncio
    async def test_order_constraint_violations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试数据库约束违反的处理"""
        # 创建订单
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",
            }
        )
        await order_repo.create(order1)

        # 尝试创建重复订单号的订单
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",  # 重复的订单号
            }
        )

        with pytest.raises(IntegrityError):
            await order_repo.create(order2)

    @pytest.mark.asyncio
    async def test_order_relationship_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试关联查询功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 测试带用户信息的查询
        order_with_user = await order_repo.get_with_user(created_order.id)
        assert order_with_user is not None
        assert order_with_user.user is not None
        assert order_with_user.user.id == test_user.id

        # 测试带申请记录的查询
        order_with_applications = await order_repo.get_with_applications(
            created_order.id
        )
        assert order_with_applications is not None

    @pytest.mark.asyncio
    async def test_order_concurrent_updates(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试并发更新处理"""
        import asyncio

        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 并发更新测试
        async def update_order_status(status: OrderStatus):
            return await order_repo.update_status(created_order.id, status)

        # 同时执行多个状态更新
        tasks = [
            update_order_status(OrderStatus.PAID),
            update_order_status(OrderStatus.PROCESSING),
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 至少有一个更新应该成功
        successful_updates = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_updates) >= 1

    @pytest.mark.asyncio
    async def test_order_audit_trail(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单审计跟踪功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 记录状态变更
        await order_repo.log_status_change(
            created_order.id, OrderStatus.CREATED, OrderStatus.PAID, "Payment received"
        )

        # 获取审计记录
        audit_logs = await order_repo.get_audit_trail(created_order.id)
        assert len(audit_logs) >= 1
        assert audit_logs[0]["from_status"] == OrderStatus.CREATED.value
        assert audit_logs[0]["to_status"] == OrderStatus.PAID.value


class TestRepositoryErrorHandlingEnhanced:
    """增强的Repository错误处理测试"""

    @pytest.mark.asyncio
    async def test_database_connection_errors(self, db_session: AsyncSession):
        """测试数据库连接错误处理"""
        # 这里可以测试数据库连接失败的情况
        # 需要根据实际的错误处理逻辑来实现
        pass

    @pytest.mark.asyncio
    async def test_transaction_timeout_handling(self, db_session: AsyncSession):
        """测试事务超时处理"""
        # 测试长时间运行的事务的超时处理
        pass

    @pytest.mark.asyncio
    async def test_deadlock_detection_and_retry(self, db_session: AsyncSession):
        """测试死锁检测和重试机制"""
        # 测试数据库死锁的检测和自动重试
        pass
