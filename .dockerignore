venv/
__pycache__/
*.pyc
*.db
*.db-journal
*.log
.git/
.github/
.vscode/
.idea/
.env
.env.*
payment_cards.json
settings.yaml
logs/
screenshots/
payment_screenshots/
downloads/
.prefs/
node_modules/
frontend/node_modules/
*_secret*.json
*_secrets*.json
*_credential*.json
*_credentials*.json
*_key*.json
*_keys*.json
*_card*.json
*_cards*.json

# 前端构建产物和开发文件
frontend/dist/
frontend/.vite/
frontend/coverage/
frontend/test-results/
frontend/playwright-report/
frontend/blob-report/

# 测试和覆盖率报告
htmlcov/
.coverage
.pytest_cache/
.mypy_cache/

# 证书和PKI目录（避免权限问题）
.pki/
*.pem
*.key
*.crt
*.p12

# 文档和配置文件（避免泄露）
README.md
docs/
*.md
docker-compose*.yml
Dockerfile*
.dockerignore
# pyproject.toml  # 注释掉，Docker 构建需要此文件
package.json
package-lock.json

# 临时和备份文件
tmp/
temp/
*.bak
*.backup
.DS_Store
Thumbs.db
