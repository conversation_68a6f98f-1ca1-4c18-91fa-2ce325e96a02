# api/main.py
"""
Vietnam E-Visa Automator API - 纯API后端重构版本

专注于API-only架构，移除所有HTML模板和静态文件依赖
"""

from contextlib import asynccontextmanager
from dataclasses import dataclass

from fastapi import Depends, FastAPI, File, UploadFile
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException

# 🔧 关键修复：初始化统一日志系统
from app.utils.logger_config import get_logger, setup_logger
from backend.auth_fastapi_users.models import User  # 添加User模型导入

# 使用新的FastAPI Users认证路由
from backend.auth_fastapi_users.routes import router as fastapi_users_auth_router
from backend.auth_fastapi_users.session_auth import (
    current_user_with_session,  # 添加会话验证依赖导入
)
from backend.config.settings import settings
from backend.core.exceptions import (
    AuthenticationError,
    FileProcessingError,
    OCRProcessingError,
    VisaApplicationError,
    authentication_exception_handler,
    file_processing_exception_handler,
    general_exception_handler,
    http_exception_handler,
    ocr_processing_exception_handler,
    validation_exception_handler,
    visa_application_exception_handler,
)
from backend.middleware.security import (
    RateLimitMiddleware,
    RequestSizeMiddleware,
    SecurityHeadersMiddleware,
)
from backend.routes.automation_logs import router as automation_logs_router
from backend.routes.email_processing import router as email_processing_router
from backend.routes.order import router as order_router  # 新增：订单管理路由
from backend.routes.visa import router as visa_router

# 🔧 关键修复：初始化统一日志系统

# 设置日志系统（UTC时区）
setup_logger(
    console_level="INFO", file_level="DEBUG", log_filename_prefix="fastapi_backend"
)
logger = get_logger()


# 移到模块级别以支持APScheduler序列化
@dataclass
class SimpleApplicant:
    email: str
    config: dict
    passport_number: str | None = None


# 全局引擎实例 - 参考旧API的做法
global_engine = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理 - 替代被弃用的 on_event"""
    global global_engine

    # 应用启动逻辑
    # ✅ 日志系统已在模块级别初始化，使用UTC时区
    logger.info(f"🚀 {settings.app_name} v{settings.app_version} 启动成功")
    logger.info(f"📊 调试模式: {'开启' if settings.debug else '关闭'}")
    logger.info("🔧 架构模式: API-only (无模板/静态文件)")
    logger.info("🕐 统一日志系统已配置（UTC时区）")

    # 统一数据库初始化，只调用一次
    try:
        from backend.db_config.initializer import DatabaseInitializer
        from backend.db_config.unified_connection import get_unified_db

        logger.info("⚙️ 正在初始化统一数据库（包含用户表、订单表、申请表等）...")
        unified_db = await get_unified_db()
        # 执行一次性数据库初始化(包含所有表结构)
        if unified_db.engine is None:
            raise RuntimeError("数据库引擎未正确初始化")
        success = await DatabaseInitializer.initialize_once(unified_db.engine)
        if success:
            logger.info("✅ 统一数据库初始化完成（包含所有表结构）")
        else:
            raise RuntimeError("数据库初始化失败")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        logger.error("🔧 解决方案:")
        logger.error("   1. 确保Docker已启动: docker-compose up -d postgres")
        logger.error("   2. 检查连接配置: POSTGRES_HOST=127.0.0.1")
        logger.error("   3. 验证端口: netstat -an | findstr :5432")
        logger.error("🚨 应用将无法正常工作，请检查数据库配置")
        # 抛出异常，阻止应用启动
        raise e

    # 初始化全局引擎
    try:
        from app.core.visa_automation_engine import VisaAutomationEngine

        global_engine = VisaAutomationEngine()
        logger.info("✅ Global VisaAutomationEngine initialized successfully.")
    except Exception as e:
        logger.error(f"❌ Failed to initialize global VisaAutomationEngine: {e}")
        global_engine = None

    # 🔥 邮件轮询已移至独立服务
    # 邮件轮询现在由独立的 email-polling 容器处理
    # 避免在容器扩展时重复轮询同一邮箱
    logger.info("📧 邮件轮询由独立服务处理，主应用不再初始化邮件调度器")

    yield  # 应用运行阶段

    # 应用关闭逻辑
    logger.info(f"🛑 {settings.app_name} 正在关闭...")

    # 🔥 邮件轮询已移至独立服务，无需在主应用中关闭
    logger.info("📧 邮件轮询由独立服务管理，主应用无需关闭邮件组件")

    # 关闭自动化引擎
    if global_engine:
        try:
            # ✅ 修复：VisaAutomationEngine没有cleanup方法，只需记录状态
            logger.info("✅ 自动化引擎已标记关闭（无需额外清理）")
        except Exception as e:
            logger.warning(f"⚠️ 关闭自动化引擎时出错: {e}")

    logger.info(f"👋 {settings.app_name} 已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Vietnam E-Visa Application Automation API - API Only Service",
    # 只在本地环境启用API文档，参考 full-stack-fastapi-template
    docs_url="/docs" if settings.environment == "local" else None,
    redoc_url="/redoc" if settings.environment == "local" else None,
    lifespan=lifespan,
)

# 添加安全中间件

# CORS 配置 - 支持现代前端开发
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite默认端口
        "http://localhost:5176",  # Vite备用端口
        "http://localhost:3000",  # React/Next.js默认端口
        "http://localhost:8000",  # FastAPI默认端口
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5176",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8000",
        "http://************:8000",  # 云服务器IP - frp隧道
    ]
    if settings.debug
    else [
        "http://************:8000",  # 云服务器IP - frp隧道
    ],
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],
)

# 生产环境安全中间件
if not settings.debug:
    # 强制 HTTPS（生产环境）
    app.add_middleware(HTTPSRedirectMiddleware)

    # 信任的主机
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=[
            "yourdomain.com",
            "*.yourdomain.com",
            "localhost",
            "127.0.0.1",
            "0.0.0.0",  # nosec B104 # 生产环境需要绑定所有接口
            "localhost:8000",
            "127.0.0.1:8000",
            "0.0.0.0:8000",
        ],
    )

# 添加安全中间件

# 请求限制中间件
app.add_middleware(RateLimitMiddleware, calls=100, period=60)

# 请求大小限制中间件
app.add_middleware(RequestSizeMiddleware, max_size=settings.max_file_size)

# 安全头中间件
app.add_middleware(SecurityHeadersMiddleware, debug=settings.debug)

# 注册异常处理器
app.add_exception_handler(VisaApplicationError, visa_application_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(OCRProcessingError, ocr_processing_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(FileProcessingError, file_processing_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(AuthenticationError, authentication_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(RequestValidationError, validation_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(StarletteHTTPException, http_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(Exception, general_exception_handler)

# 注册路由
app.include_router(fastapi_users_auth_router)
app.include_router(visa_router, prefix="/api")
app.include_router(order_router, prefix="/api")  # 新增：注册订单路由

# 注册 automation_logs 路由
app.include_router(automation_logs_router)

# 注册邮件处理路由
app.include_router(email_processing_router, prefix="/api")

# 注意：visa_router已经在上面注册，不要重复注册


@app.get("/")
async def api_info():
    """
    API服务信息 - 根路径
    """
    return {
        "service": "Vietnam E-Visa Automator API",
        "version": settings.app_version,
        "status": "running",
        "api_docs": "/docs",
        "message": "API-only service",
    }


@app.get("/health")
async def health_check():
    """健康检查端点 - 用于CI/CD和监控"""
    from datetime import datetime
    from zoneinfo import ZoneInfo

    from sqlalchemy import text

    from backend.db_config.unified_connection import get_unified_db

    try:
        # 检查数据库连接
        unified_db = await get_unified_db()
        if unified_db.engine is None:
            raise RuntimeError("数据库引擎未初始化")

        # 简单的数据库连接测试
        async with unified_db.engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            if result.scalar() != 1:
                raise RuntimeError("数据库连接异常")

        return {
            "status": "healthy",
            "timestamp": datetime.now(ZoneInfo("UTC")).isoformat(),
            "database": "connected",
            "engine": "initialized" if global_engine else "not_initialized",
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(ZoneInfo("UTC")).isoformat(),
            "error": str(e),
        }


# 兼容旧API的OCR端点
@app.post("/ocr-passport/", tags=["OCR"])
async def ocr_passport_legacy_endpoint(
    passport_scan: UploadFile = File(...),
    # 🔧 修复：恢复会话验证，确保API安全性
    current_user: User = Depends(current_user_with_session),
):
    """
    护照OCR识别 (兼容旧API，恢复会话验证)

    这个端点与旧API保持完全一致，直接返回OCR字段而不包装在response对象中
    恢复会话验证以确保API安全性，真正的问题在于前端双重对话框处理
    """
    from datetime import datetime
    from pathlib import Path
    import uuid
    from zoneinfo import ZoneInfo

    from app.data.applicant_mapper import normalize_ocr_result_for_applicant
    from app.utils.ocr_utils import run_aliyun_passport_ocr

    # 生成UUID和UTC时间戳的文件名
    file_uuid = str(uuid.uuid4())
    utc_tz = ZoneInfo("UTC")
    utc_time = datetime.now(utc_tz).strftime("%Y%m%d_%H%M%S")
    original_filename = passport_scan.filename or "passport.jpg"
    temp_filename = f"{file_uuid}_{utc_time}_{original_filename}"

    # 使用安全的临时目录
    import tempfile

    temp_dir = Path(tempfile.gettempdir()) / "visa_automator"
    temp_dir.mkdir(exist_ok=True)
    temp_passport_path = temp_dir / temp_filename

    try:
        # 写入临时文件
        with open(temp_passport_path, "wb") as temp_file:
            content = await passport_scan.read()
            temp_file.write(content)

        # 执行OCR处理
        ocr_result = run_aliyun_passport_ocr(str(temp_passport_path))
        fields = normalize_ocr_result_for_applicant(ocr_result) if ocr_result else {}
        return fields  # 直接返回字段，与旧API保持一致

    except Exception as e:
        logger.error(f"❌ OCR处理失败 (UTC时间: {utc_time}): {e}")
        raise
    finally:
        # 强制清理临时文件
        try:
            if temp_passport_path.exists():
                temp_passport_path.unlink()
                logger.debug(f"✅ 临时文件已清理: {temp_filename}")
        except Exception as cleanup_error:
            logger.warning(f"⚠️ 清理临时文件失败 (UTC时间: {utc_time}): {cleanup_error}")


# 注意：启动和关闭逻辑已移至 lifespan 函数，符合FastAPI最新最佳实践


# 注意：签证申请端点在 api/routes/visa.py 中定义
# 路径为 /api/visa/apply (通过 visa_router + prefix="/api")


# 开发环境启动
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
