"""
Repository层真实方法测试
======================

只测试实际存在的方法，提高真实代码覆盖率
避免调用不存在的方法，专注于现有代码路径
"""

from datetime import datetime
import uuid

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.applicant_repository import ApplicantRepository
from app.repositories.application_repository import ApplicationRepository
from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestOrderRepositoryRealMethods:
    """测试OrderRepository的实际存在方法"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_get_orders_by_status_with_user_id(
        self, order_repo: OrderRepository, test_user
    ):
        """测试按状态查询订单（必须提供user_id）"""
        # 创建不同状态的订单
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CREATED.value,
            }
        )
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CANCELLED.value,
            }
        )

        await order_repo.create(order1)
        await order_repo.create(order2)

        # 测试按状态查询（必须提供user_id）
        created_orders = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders) >= 1
        assert all(o.order_status == OrderStatus.CREATED.value for o in created_orders)

        # 测试不提供user_id会抛出安全错误
        with pytest.raises(ValueError, match="安全错误：必须提供user_id进行权限控制"):
            await order_repo.get_orders_by_status(OrderStatus.CREATED)

    @pytest.mark.asyncio
    async def test_get_order_stats_structure(
        self, order_repo: OrderRepository, test_user
    ):
        """测试订单统计的实际返回结构"""
        # 创建订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CREATED.value,
            }
        )
        await order_repo.create(order)

        # 测试用户统计
        user_stats = await order_repo.get_order_stats(test_user.id)
        assert isinstance(user_stats, dict)
        assert "total_orders" in user_stats
        assert user_stats["total_orders"] >= 1

        # 测试全局统计
        global_stats = await order_repo.get_order_stats()
        assert isinstance(global_stats, dict)
        assert "total_orders" in global_stats
        assert global_stats["total_orders"] >= 1

    @pytest.mark.asyncio
    async def test_pagination_comprehensive(
        self, order_repo: OrderRepository, test_user
    ):
        """测试分页查询的全面功能"""
        # 创建足够多的订单
        total_orders = 8
        for i in range(total_orders):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711PAGINATE{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试不同的分页参数
        test_cases = [
            {"limit": 3, "offset": 0, "expected_min": 3},
            {"limit": 3, "offset": 3, "expected_min": 3},
            {"limit": 3, "offset": 6, "expected_min": 2},
            {"limit": 3, "offset": 10, "expected_min": 0},
        ]

        for case in test_cases:
            result = await order_repo.get_by_user_id(
                test_user.id, limit=case["limit"], offset=case["offset"]
            )
            assert len(result) >= case["expected_min"]

        # 测试无限制查询
        all_orders = await order_repo.get_by_user_id(test_user.id)
        assert len(all_orders) >= total_orders

    @pytest.mark.asyncio
    async def test_order_crud_complete_cycle(
        self, order_repo: OrderRepository, test_user
    ):
        """测试订单的完整CRUD周期"""
        # Create
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CREATED.value,
            }
        )
        created_order = await order_repo.create(order)
        assert created_order.id is not None
        original_id = created_order.id

        # Read by ID
        found_order = await order_repo.get_by_id(original_id)
        assert found_order is not None
        assert found_order.id == original_id

        # Read by order_no
        found_by_no = await order_repo.get_by_order_no(
            created_order.order_no, test_user.id
        )
        assert found_by_no is not None
        assert found_by_no.id == original_id

        # Update
        found_order.order_status = OrderStatus.CANCELLED.value
        found_order.updated_at = datetime.now()
        updated_order = await order_repo.update(found_order)
        assert updated_order.order_status == OrderStatus.CANCELLED.value

        # Verify update
        verified_order = await order_repo.get_by_id(original_id)
        assert verified_order.order_status == OrderStatus.CANCELLED.value

        # Delete
        deleted = await order_repo.delete(original_id)
        assert deleted is True

        # Verify deletion
        deleted_order = await order_repo.get_by_id(original_id)
        assert deleted_order is None

    @pytest.mark.asyncio
    async def test_error_scenarios_comprehensive(
        self, order_repo: OrderRepository, test_user
    ):
        """测试各种错误场景"""
        # 测试查询不存在的ID
        fake_id = uuid.uuid4()
        non_existent = await order_repo.get_by_id(fake_id)
        assert non_existent is None

        # 测试查询不存在的订单号
        non_existent_no = await order_repo.get_by_order_no(
            "VN20250101NOTEXIST", test_user.id
        )
        assert non_existent_no is None

        # 测试无效用户ID查询
        fake_user_id = uuid.uuid4()
        empty_result = await order_repo.get_by_user_id(fake_user_id)
        assert len(empty_result) == 0

        # 测试删除不存在的订单
        delete_result = await order_repo.delete(fake_id)
        assert delete_result is False

    @pytest.mark.asyncio
    async def test_constraint_and_integrity(
        self, order_repo: OrderRepository, test_user
    ):
        """测试数据完整性约束"""
        # 创建订单
        unique_order_no = "VN20250711CONSTRAINT001"
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": unique_order_no,
            }
        )
        await order_repo.create(order1)

        # 尝试创建重复订单号
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": unique_order_no,
            }
        )

        with pytest.raises(IntegrityError):
            await order_repo.create(order2)


class TestApplicantRepositoryRealMethods:
    """测试ApplicantRepository的实际方法"""

    @pytest.fixture
    async def applicant_repo(self, db_session: AsyncSession):
        return ApplicantRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_applicant_basic_operations(
        self, applicant_repo: ApplicantRepository, test_user
    ):
        """测试申请人的基本操作"""
        # Create
        applicant = TestDataFactory.create_applicant_data(
            {
                "user_id": test_user.id,
                "applicant_name": "测试申请人",
                "passport_number": "E12345678",
            }
        )
        created_applicant = await applicant_repo.create(applicant)
        assert created_applicant.id is not None

        # Read
        found_applicant = await applicant_repo.get_by_id(created_applicant.id)
        assert found_applicant is not None
        assert found_applicant.applicant_name == "测试申请人"

        # Update
        found_applicant.applicant_name = "更新后的申请人"
        updated_applicant = await applicant_repo.update(found_applicant)
        assert updated_applicant.applicant_name == "更新后的申请人"

        # Delete
        deleted = await applicant_repo.delete(created_applicant.id)
        assert deleted is True

    @pytest.mark.asyncio
    async def test_applicant_query_methods(
        self, applicant_repo: ApplicantRepository, test_user
    ):
        """测试申请人查询方法"""
        # 创建申请人
        applicant = TestDataFactory.create_applicant_data(
            {
                "user_id": test_user.id,
                "passport_number": "E87654321",
            }
        )
        await applicant_repo.create(applicant)

        # 测试按护照号查询（正确的参数数量）
        found_by_passport = await applicant_repo.get_by_passport_number("E87654321")
        assert found_by_passport is not None
        assert found_by_passport.passport_number == "E87654321"

        # 测试按用户查询
        user_applicants = await applicant_repo.get_by_user_id(test_user.id)
        assert len(user_applicants) >= 1


class TestApplicationRepositoryRealMethods:
    """测试ApplicationRepository的实际方法"""

    @pytest.fixture
    async def application_repo(self, db_session: AsyncSession):
        return ApplicationRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_application_basic_crud(
        self, application_repo: ApplicationRepository, test_user
    ):
        """测试申请记录的基本CRUD操作"""
        # Create
        application = TestDataFactory.create_application_data(
            {
                "user_id": test_user.id,
                "application_number": "VN20250711APP001",
            }
        )
        created_application = await application_repo.create(application)
        assert created_application.id is not None

        # Read
        found_application = await application_repo.get_by_id(created_application.id)
        assert found_application is not None
        assert found_application.application_number == "VN20250711APP001"

        # Update
        found_application.visa_status = "approved"
        updated_application = await application_repo.update(found_application)
        assert updated_application.visa_status == "approved"

        # Delete
        deleted = await application_repo.delete(created_application.id)
        assert deleted is True

    @pytest.mark.asyncio
    async def test_application_query_operations(
        self, application_repo: ApplicationRepository, test_user
    ):
        """测试申请记录的查询操作"""
        # 创建申请记录
        application = TestDataFactory.create_application_data(
            {
                "user_id": test_user.id,
                "application_number": "VN20250711QUERY001",
                "visa_status": "submitted",
            }
        )
        created_application = await application_repo.create(application)

        # 测试按申请号查询
        found_by_number = await application_repo.get_by_application_number(
            "VN20250711QUERY001"
        )
        assert found_by_number is not None
        assert found_by_number.id == created_application.id

        # 测试错误处理
        not_found = await application_repo.get_by_application_number("NOTEXIST")
        assert not_found is None


class TestRepositoryPerformanceAndEdgeCases:
    """测试Repository的性能和边界情况"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_large_dataset_pagination(
        self, order_repo: OrderRepository, test_user
    ):
        """测试大数据集的分页性能"""
        # 创建较多订单
        batch_size = 20
        for i in range(batch_size):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711PERF{str(i).zfill(4)}",
                }
            )
            await order_repo.create(order)

        # 测试不同分页大小
        page_sizes = [5, 10, 15]
        for page_size in page_sizes:
            page1 = await order_repo.get_by_user_id(
                test_user.id, limit=page_size, offset=0
            )
            assert len(page1) == min(page_size, batch_size)

    @pytest.mark.asyncio
    async def test_concurrent_access_simulation(
        self, order_repo: OrderRepository, test_user
    ):
        """模拟并发访问场景"""
        import asyncio

        # 创建基础订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
            }
        )
        created_order = await order_repo.create(order)

        # 模拟并发读取
        async def read_order():
            return await order_repo.get_by_id(created_order.id)

        # 并发执行多个读取操作
        tasks = [read_order() for _ in range(5)]
        results = await asyncio.gather(*tasks)

        # 验证所有读取都成功
        assert all(result is not None for result in results)
        assert all(result.id == created_order.id for result in results)

    @pytest.mark.asyncio
    async def test_edge_case_data_values(self, order_repo: OrderRepository, test_user):
        """测试边界数据值"""
        # 测试空字符串和None值的处理
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "applicant_name": "",  # 空字符串
                "order_status": OrderStatus.CREATED.value,
            }
        )
        created_order = await order_repo.create(order)
        assert created_order.id is not None

        # 验证数据正确保存
        found_order = await order_repo.get_by_id(created_order.id)
        assert found_order.applicant_name == ""
