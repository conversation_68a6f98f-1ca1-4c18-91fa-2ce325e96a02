"""
统一时间管理工具 - 后端Python版本
================================

与前端 timeFormat.ts 保持一致的时间处理原则：
- 后端统一提供UTC时间戳
- 使用标准ISO格式
- 确保时区处理的一致性

核心原则：
- 内部存储使用UTC
- 显示时转换为用户时区
- 统一的时间格式化函数
"""

from datetime import datetime, timezone
from zoneinfo import ZoneInfo
from typing import Optional


# 默认时区配置 - 业务时区
DEFAULT_TIMEZONE = "Asia/Shanghai"
UTC_TIMEZONE = timezone.utc


def get_current_utc_timestamp() -> str:
    """
    获取当前UTC时间戳（ISO格式）
    
    Returns:
        str: ISO格式的UTC时间戳字符串
    """
    return datetime.now(UTC_TIMEZONE).isoformat()


def get_current_utc_datetime() -> datetime:
    """
    获取当前UTC时间对象
    
    Returns:
        datetime: UTC时间对象
    """
    return datetime.now(UTC_TIMEZONE)


def format_datetime_for_display(
    dt: Optional[datetime], 
    timezone_str: str = DEFAULT_TIMEZONE,
    format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """
    格式化datetime对象为显示字符串
    
    Args:
        dt: datetime对象
        timezone_str: 目标时区
        format_str: 格式化字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    if dt is None:
        return "—"
    
    try:
        # 确保datetime有时区信息
        if dt.tzinfo is None:
            # 假设无时区信息的datetime是UTC
            dt = dt.replace(tzinfo=UTC_TIMEZONE)
        
        # 转换到目标时区
        target_tz = ZoneInfo(timezone_str)
        local_dt = dt.astimezone(target_tz)
        
        return local_dt.strftime(format_str)
    except Exception:
        return "时间格式错误"


def parse_iso_timestamp(iso_string: str) -> Optional[datetime]:
    """
    解析ISO格式时间戳字符串
    
    Args:
        iso_string: ISO格式时间戳字符串
        
    Returns:
        datetime: 解析后的datetime对象（UTC时区）
    """
    if not iso_string:
        return None
    
    try:
        # 处理不同的ISO格式
        if iso_string.endswith('Z'):
            iso_string = iso_string[:-1] + '+00:00'
        
        dt = datetime.fromisoformat(iso_string)
        
        # 确保转换为UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=UTC_TIMEZONE)
        else:
            dt = dt.astimezone(UTC_TIMEZONE)
            
        return dt
    except Exception:
        return None


def validate_timezone(timezone_str: str) -> bool:
    """
    验证时区字符串是否有效
    
    Args:
        timezone_str: 时区字符串
        
    Returns:
        bool: 是否有效
    """
    try:
        ZoneInfo(timezone_str)
        return True
    except Exception:
        return False


def get_safe_timezone(timezone_str: str) -> str:
    """
    获取安全的时区字符串，无效时返回默认时区
    
    Args:
        timezone_str: 时区字符串
        
    Returns:
        str: 有效的时区字符串
    """
    if validate_timezone(timezone_str):
        return timezone_str
    return DEFAULT_TIMEZONE


def datetime_to_iso_string(dt: datetime) -> str:
    """
    将datetime对象转换为ISO字符串
    
    Args:
        dt: datetime对象
        
    Returns:
        str: ISO格式字符串
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC_TIMEZONE)
    else:
        dt = dt.astimezone(UTC_TIMEZONE)
    
    return dt.isoformat()


def ensure_utc_datetime(dt: datetime) -> datetime:
    """
    确保datetime对象是UTC时区
    
    Args:
        dt: datetime对象
        
    Returns:
        datetime: UTC时区的datetime对象
    """
    if dt.tzinfo is None:
        return dt.replace(tzinfo=UTC_TIMEZONE)
    return dt.astimezone(UTC_TIMEZONE)


# 常用时间格式常量
class TimeFormats:
    """时间格式常量"""
    DATETIME = "%Y-%m-%d %H:%M:%S"
    DATE_ONLY = "%Y-%m-%d"
    TIME_ONLY = "%H:%M:%S"
    RELATIVE = "%m月%d日 %H:%M"
    SHORT_DATE = "%m-%d %H:%M"
    ISO_FORMAT = "%Y-%m-%dT%H:%M:%S%z"
