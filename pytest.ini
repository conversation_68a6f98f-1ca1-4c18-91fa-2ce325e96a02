[pytest]
# pytest 配置文件 - 修复版本
# 配置测试环境和运行参数

# 测试发现路径
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test* *Tests

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov=backend
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=50
    --asyncio-mode=auto
    --disable-warnings

# 标记定义
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    auth: marks tests as authentication tests
    mock: marks tests that use extensive mocking
    database: marks tests that require database
    external: marks tests that require external services
    performance: marks tests that measure performance
    session_management: marks tests for Redis session management
    jwt: marks tests for JWT token operations
    redis: marks tests that depend on Redis functionality
    real_website: marks tests that access real websites (Vietnam e-visa official site)
    real_automation: marks tests that perform real automation flows

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:asyncio
    ignore::RuntimeWarning:asyncio
    ignore::pytest.PytestUnraisableExceptionWarning

# 异步测试配置
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试文件编码
doctest_encoding = utf-8

# 并发测试
# 取消注释以启用并发测试
# -n auto

# 覆盖率配置
[coverage:run]
source = app, backend
omit =
    */tests/*
    */test_*
    */conftest.py
    */migrations/*
    */venv/*
    */env/*
    */__pycache__/*
    */.*
    */setup.py
    */manage.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
